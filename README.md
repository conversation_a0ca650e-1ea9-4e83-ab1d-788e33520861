# playwright-mind

基于 Playwright 和 midscene.js 的 AI 自动化测试项目，为 Web UI 测试注入 AI 智能能力。



## 目录结构简述

```
tests/
├── data/         # 测试数据配置（test-data.ts, urls.ts）
├── fixture/      # Playwright 测试夹具与断言
├── pages/        # 页面对象模型（POM）
├── utils/        # 通用工具类
├── laoyaoba/     # laoyaoba业务测试用例
│   ├── auth.setup.ts                           # 认证设置
│   ├── laoyaoba-news.spec.ts                   # 新闻数据获取测试
│   └── laoyaoba-homepage-news-validation.spec.ts # 首页新闻列表验证测试
```

---

## 设计模式说明

- **页面对象模型（POM）**：每个页面有独立类，封装页面操作，提升可维护性与复用性。
- **测试数据分离**：所有测试数据、URL、AI指令等均集中管理，便于维护。
- **工具类**：提供常用辅助方法，如时间、字符串处理、兼容性补丁等。

---

## 示例用例

### 1. 首页新闻列表验证测试

```typescript
import { test } from '../fixture/fixture';
import { LaoyaobaHomePage } from '../pages';

test('validate-homepage-news-list-display', async ({ page, ai, aiQuery, assert }) => {
  const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);

  // 访问首页
  await homePage.visit();

  // 验证首页新闻列表内容完整性
  const { count, titles } = await homePage.validateHomeNewsListContent();

  // 验证新闻数量和标题质量
  assert.isTruthy(count >= 5, `首页新闻列表应至少包含5条新闻`);
  titles.forEach((title, index) => {
    assert.isTruthy(title && title.trim().length > 0, `新闻标题不应为空`);
  });
});
```

### 2. 舆情数据获取测试

```typescript
import { test } from '../fixture/fixture';
import { LaoyaobaHomePage, OpinionPage } from '../pages';

test('获取最新舆情', async ({ page, ai, aiQuery, assert }) => {
  const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);
  const opinionPage = new OpinionPage(page, assert, ai, aiQuery);

  await homePage.navigateToOpinion();
  await opinionPage.navigateToHotNews();

  const time = await opinionPage.getFirstNewsTime();
  assert.isTruthy(time, '时间格式应有效');
});
```

---

## 安装与配置

1. 克隆项目到本地：

```shell
git clone https://github.com/autotestclass/playwright-mind
```

2. 安装依赖

```shell
npm install --registry=https://registry.npmmirror.com
```

3. 安装运行浏览器

```shell
npx playwright install
```

4. 配置大模型

> 本项目默认使用 `qwen-vl-max-latest` 模型, 经过验证可用，关键是免费。如果想其他模型请参考midscenejs官方配置。

阿里云百练：https://bailian.console.aliyun.com/

使用其他模型：https://midscenejs.com/zh/model-provider.html

在 `.env` 文件中配置环境变量：

```ts
export OPENAI_API_KEY="sk-your-key"
export OPENAI_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
export MIDSCENE_MODEL_NAME="qwen-vl-max-latest"
```

三种关键方法：交互（.ai, .aiAction）, 提取 (.aiQuery), 断言 (.aiAssert)。

* `.ai`方法描述步骤并执行交互
* `.aiQuery` 从 UI 中“理解”并提取数据，返回值是 JSON 格式，你可以尽情描述想要的数据结构
* `.aiAssert` 来执行断言

__运行测试__

```shell
# 运行所有laoyaoba相关测试（推荐 - 动态worker配置）
npm run test:laoyaoba

# 运行所有laoyaoba相关测试（优化配置）
npm run test:optimized tests/laoyaoba/

# 运行所有laoyaoba测试（基础配置）
npm run test:laoyaoba-basic

# 单独运行首页新闻列表验证测试
npm run test:homepage-news

# 验证动态worker配置
node test-worker-config.js

# 基本运行方式（单个测试文件）
npx playwright test --headed tests/laoyaoba/laoyaoba-news.spec.ts
