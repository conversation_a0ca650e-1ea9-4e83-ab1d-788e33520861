const { AiCallMonitor } = require('./tests/utils/ai-call-monitor.ts');

console.log('Testing AI report functionality...');

// 模拟一些AI调用
const callId1 = AiCallMonitor.startCall('query', 'test query 1');
setTimeout(() => {
  AiCallMonitor.endCall(callId1, true, false);
}, 100);

const callId2 = AiCallMonitor.startCall('action', 'test action 1');
setTimeout(() => {
  AiCallMonitor.endCall(callId2, true, true); // 缓存命中
}, 200);

setTimeout(() => {
  console.log('\nMerging to global stats...');
  AiCallMonitor.mergeToGlobalStats();
  
  console.log('\nPrinting global report...');
  AiCallMonitor.printGlobalReport();
  
  setTimeout(() => {
    console.log('\nCleaning up...');
    AiCallMonitor.cleanupStatsFiles();
  }, 1000);
}, 500);
