{"name": "playwright-mind", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:windows": "node scripts/run-tests-windows.js", "test:laoyaoba": "node scripts/dynamic-worker-runner.js", "test:laoyaoba-basic": "node scripts/run-tests-windows.js tests/laoyaoba/", "test:laoyaoba-fixed": "node scripts/run-tests-windows.js --config=playwright.laoyaoba.config.ts", "test:homepage-news": "node scripts/run-tests-windows.js tests/laoyaoba/laoyaoba-homepage-news-validation.spec.ts", "test:simple": "node scripts/run-tests-windows.js tests/laoyaoba/laoyaoba-simple-test.spec.ts", "test:optimized": "AI_CACHE_ENABLED=true AI_BATCH_ENABLED=true playwright test", "test:debug": "AI_DEBUG=true AI_CACHE_ENABLED=false AI_LOG_LEVEL=verbose playwright test", "test:ci": "TEST_ENV=ci AI_CACHE_ENABLED=true AI_BATCH_ENABLED=true playwright test", "clean:cache": "node scripts/clean-cache.js clean", "clean:cache:remove": "node scripts/clean-cache.js remove", "clean:cache:only": "node scripts/clean-cache.js cache", "pretest": "node scripts/clean-cache.js clean"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@midscene/web": "^0.9.2", "@playwright/test": "^1.50.1", "@types/node": "^22.16.5", "axios": "^1.9.0"}, "dependencies": {"@executeautomation/playwright-mcp-server": "^0.2.7", "dotenv": "^16.4.7", "playwright-mind": "file:"}}