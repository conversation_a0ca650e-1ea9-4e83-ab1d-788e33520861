import { test } from '../fixture/fixture';
import { LaoyaobaHomePage } from '../pages';

/**
 * laoyaoba首页新闻列表内容验证测试
 * 验证访问laoyaoba主页时，新闻列表内容展示正常
 * 使用串行模式，在单个浏览器实例中顺序执行所有测试场景
 */
test.describe.serial('laoyaoba-homepage-news-validation', () => {

  test('laoyaoba-homepage-news-validation-full', async ({ page, ai, aiQuery, assert }) => {

    // 创建首页页面对象
    const homePage = new LaoyaobaHomePage(page, assert, ai, aiQuery);

    // 访问首页（只访问一次）
    await homePage.visit();

    // 验证页面是否正确加载
    const currentUrl = page.url();

    if (!currentUrl.includes('laoyaoba.com')) {
      throw new Error(`页面未正确加载，当前URL: ${currentUrl}`);
    }

    // ========== 测试场景: 验证首页新闻列表显示 ==========
    // 先验证新闻列表是否可见
    await homePage.verifyHomeNewsListDisplay();

    // 尝试获取新闻数量和标题
    const count = await homePage.getHomeNewsListCount();
    await homePage.getHomeNewsListTitles();

    // 验证新闻数量合理性（降低要求，至少1条）
    assert.isTruthy(count >= 1, `首页新闻列表应至少包含1条新闻，实际获取到 ${count} 条`);
  });

});
