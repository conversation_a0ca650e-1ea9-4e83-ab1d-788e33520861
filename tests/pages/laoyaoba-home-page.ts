import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';
import { OptimizedAiQueries } from '../utils/optimized-ai-queries';
import { BatchAiOperations, CommonBatchOperations } from '../utils/batch-ai-operations';

/**
 * laoyaoba首页页面对象
 */
export class LaoyaobaHomePage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 访问首页
   */
  async visit(): Promise<void> {
    // 添加重试机制
    const maxRetries = 3;
    let lastError: Error = new Error('未知错误');

    for (let i = 0; i < maxRetries; i++) {
      try {
        await this.navigateToHome();

        // 验证页面是否正确加载
        const currentUrl = this.getCurrentUrl();

        // 如果URL包含laoyaoba.com，认为访问成功
        if (currentUrl.includes('laoyaoba.com')) {
          return;
        } else {
          throw new Error(`页面URL不正确: ${currentUrl}`);
        }

      } catch (error) {
        lastError = error;

        if (i < maxRetries - 1) {
          await this.page.waitForTimeout(2000);
        }
      }
    }

    // 所有重试都失败了
    throw lastError;
  }

  /**
   * 执行登录操作（优化版）
   */
  async login(): Promise<void> {
    // 使用批量操作执行登录序列
    const loginOperations = CommonBatchOperations.loginSequence();
    const results = await BatchAiOperations.executeIntelligentBatch(this, loginOperations);

    // 分析结果
    const analysis = BatchAiOperations.analyzeBatchResults(results);

    // 检查是否有关键操作失败
    if (analysis.successRate < 80) {
      throw new Error(`登录操作失败率过高: ${(100 - analysis.successRate).toFixed(1)}%`);
    }

    // 等待登录成功，返回首页
    await this.page.waitForURL(URLs.base, { timeout: TestData.timeouts.login });
  }

  /**
   * 导航到舆情页面
   */
  async navigateToOpinion(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickOpinion,
      URLs.fragments.opinion,
      'URL should navigate to the opinion page'
    );
  }

  /**
   * 导航到最新新闻页面
   */
  async navigateToLatestNews(): Promise<void> {
    await this.navigateByAiAction(
      TestData.aiCommands.navigation.clickLatest,
      URLs.fragments.news,
      'URL should navigate to the news page'
    );
  }

  /**
   * 保存认证状态
   * @param authFilePath - 认证文件路径
   */
  async saveAuthState(authFilePath: string = TestData.auth.authFile): Promise<void> {
    await this.page.context().storageState({ path: authFilePath });
  }

  /**
   * 验证首页新闻列表是否正常显示（优化版）
   */
  async verifyHomeNewsListDisplay(): Promise<void> {
    // 检查页面内容是否加载
    const bodyContent = await this.page.textContent('body');
    if (!bodyContent || bodyContent.trim().length === 0) {
      throw new Error('页面内容为空，可能未正确加载');
    }

    // 等待页面加载完成
    await this.waitForPageLoad();

    // 使用优化的复合查询获取新闻列表信息
    const newsInfo = await OptimizedAiQueries.getHomeNewsListInfo(this);

    if (!newsInfo.visible) {
      throw new Error(TestData.messages.errors.newsListNotVisible);
    }
  }

  /**
   * 获取首页新闻列表数量（优化版）
   * @returns 新闻条目数量
   */
  async getHomeNewsListCount(): Promise<number> {
    const newsInfo = await OptimizedAiQueries.getHomeNewsListInfo(this);

    this.assert.isTruthy(newsInfo.count > 0, `${TestData.messages.errors.newsListEmpty}，获取到数量: ${newsInfo.count}`);
    return newsInfo.count;
  }

  /**
   * 获取首页新闻列表标题（优化版）
   * @returns 新闻标题数组
   */
  async getHomeNewsListTitles(): Promise<string[]> {
    const newsInfo = await OptimizedAiQueries.getHomeNewsListInfo(this);

    this.assert.isTruthy(newsInfo.titles.length > 0, `${TestData.messages.errors.newsListTitlesEmpty}，获取到标题数量: ${newsInfo.titles.length}`);
    return newsInfo.titles;
  }

  /**
   * 验证首页新闻列表内容完整性（优化版）
   * @returns 包含新闻数量和标题的对象
   */
  async validateHomeNewsListContent(): Promise<{ count: number; titles: string[] }> {
    // 等待页面加载完成
    await this.waitForPageLoad();

    // 使用单次复合查询获取所有信息
    const newsInfo = await OptimizedAiQueries.getHomeNewsListInfo(this);

    // 验证新闻列表可见性
    this.assert.isTruthy(newsInfo.visible, TestData.messages.errors.newsListNotVisible);

    // 验证新闻数量
    this.assert.isTruthy(newsInfo.count > 0, `${TestData.messages.errors.newsListEmpty}，获取到数量: ${newsInfo.count}`);

    // 验证新闻标题
    this.assert.isTruthy(newsInfo.titles.length > 0, `${TestData.messages.errors.newsListTitlesEmpty}，获取到标题数量: ${newsInfo.titles.length}`);

    return { count: newsInfo.count, titles: newsInfo.titles };
  }
}
