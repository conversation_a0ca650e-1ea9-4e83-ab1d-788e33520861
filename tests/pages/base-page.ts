import { Page } from '@playwright/test';
import { Assert } from '../utils/assertions';
import { URLs } from '../data/urls';
import { TestData } from '../data/test-data';
import { AiCallCache } from '../utils/unified-midscene-utils';
import { AiCallMonitor } from '../utils/ai-call-monitor';
import { SmartRetryManager, RetryConfigs } from '../utils/smart-retry-manager';

/**
 * 基础页面类
 * 包含所有页面共用的方法和属性
 */
export abstract class BasePage {
  protected page: Page;
  protected assert: Assert;
  protected ai: any;
  protected aiQuery: any;

  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    this.page = page;
    this.assert = assert;
    this.ai = ai;
    this.aiQuery = aiQuery;
  }

  /**
   * 导航到指定 URL
   * @param url - 目标 URL
   */
  async navigateTo(url: string): Promise<void> {
    try {
      const response = await this.page.goto(url, {
        waitUntil: 'networkidle',
        timeout: 30000
      });

      if (!response) {
        throw new Error('页面导航失败：没有收到响应');
      }

      if (!response.ok()) {
        throw new Error(`页面响应状态异常: ${response.status()} ${response.statusText()}`);
      }
    } catch (error) {
      throw new Error(`导航失败到 ${url}: ${error.message}`);
    }
  }

  /**
   * 导航到首页
   */
  async navigateToHome(): Promise<void> {
    await this.navigateTo(URLs.base);
  }

  /**
   * 等待页面加载完成
   * @param timeout - 超时时间，默认使用配置中的值
   */
  async waitForPageLoad(timeout: number = TestData.timeouts.networkIdle): Promise<void> {
    await this.page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * 等待 URL 包含指定内容
   * @param urlFragment - URL 片段
   * @param errorMessage - 错误消息
   */
  async waitForUrlContains(urlFragment: string, errorMessage?: string): Promise<void> {
    await this.assert.urlToContain(urlFragment, errorMessage || TestData.messages.errors.urlNavigation);
  }

  /**
   * 执行单条 AI 操作（带监控和智能重试）
   * @param command - AI 指令
   * @param useRetry - 是否使用智能重试，默认true
   */
  async performAiAction(command: string, useRetry: boolean = true): Promise<void> {
    const callId = AiCallMonitor.startCall('action', command, this.getCurrentUrl());

    if (!useRetry) {
      // 不使用重试的简单执行
      try {
        await this.ai(command);
        AiCallMonitor.endCall(callId, true);
      } catch (error) {
        AiCallMonitor.endCall(callId, false, false, error.message);
        throw error;
      }
      return;
    }

    // 使用智能重试执行
    const result = await SmartRetryManager.executeWithConditionalRetry(
      async () => await this.ai(command),
      SmartRetryManager.isRetryableError,
      undefined, // AI操作通常没有降级策略
      RetryConfigs.standard
    );

    if (result.success) {
      AiCallMonitor.endCall(callId, true, false);
    } else {
      AiCallMonitor.endCall(callId, false, false, result.error?.message);
      throw result.error || new Error('AI操作失败');
    }
  }

  /**
   * 批量执行 AI 操作
   * @param commands - AI 指令数组
   */
  async performAiActions(commands: string[]): Promise<void> {
    for (const cmd of commands) {
      await this.performAiAction(cmd);
    }
  }

  /**
   * 通用 AI 导航方法
   * @param aiCommand - AI 导航指令
   * @param urlFragment - 期望 URL 片段
   * @param errorMessage - 错误信息
   */
  async navigateByAiAction(aiCommand: string, urlFragment: string, errorMessage?: string): Promise<void> {
    await this.performAiAction(aiCommand);
    await this.waitForUrlContains(urlFragment, errorMessage);
  }

  /**
   * 执行 AI 查询（带缓存、监控和智能重试）
   * @param query - 查询指令
   * @param useCache - 是否使用缓存，默认true
   * @param cacheTTL - 缓存时间（毫秒），默认5分钟
   * @param useRetry - 是否使用智能重试，默认true
   * @returns 查询结果
   */
  async performAiQuery(
    query: string,
    useCache: boolean = true,
    cacheTTL: number = 5 * 60 * 1000,
    useRetry: boolean = true
  ): Promise<string> {
    const pageUrl = this.getCurrentUrl();
    const cacheKey = AiCallCache.generateCacheKey(query, pageUrl);

    // 检查缓存
    if (useCache) {
      const cachedResult = AiCallCache.get(cacheKey);
      if (cachedResult !== null) {
        AiCallMonitor.recordCacheHit('query', query);
        return cachedResult;
      }
    }

    // 执行AI查询
    const callId = AiCallMonitor.startCall('query', query, pageUrl);

    if (!useRetry) {
      // 不使用重试的简单执行
      try {
        const result = await this.aiQuery(query);

        if (useCache) {
          AiCallCache.set(cacheKey, result, cacheTTL);
        }

        AiCallMonitor.endCall(callId, true);
        return result;
      } catch (error) {
        AiCallMonitor.endCall(callId, false, false, error.message);
        throw error;
      }
    }

    // 使用智能重试执行
    const result = await SmartRetryManager.executeWithConditionalRetry(
      async () => await this.aiQuery(query),
      SmartRetryManager.isRetryableError,
      undefined, // AI查询通常没有降级策略
      RetryConfigs.standard
    );

    if (result.success && result.result) {
      // 缓存结果
      if (useCache) {
        AiCallCache.set(cacheKey, result.result, cacheTTL);
      }

      AiCallMonitor.endCall(callId, true, false);
      return result.result;
    } else {
      AiCallMonitor.endCall(callId, false, false, result.error?.message);
      throw result.error || new Error('AI查询失败');
    }
  }

  /**
   * 获取当前页面 URL
   * @returns 当前页面 URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * 验证页面标题
   * @param expectedTitle - 期望的标题
   */
  async verifyPageTitle(expectedTitle: string): Promise<void> {
    const title = await this.page.title();
    this.assert.isTruthy(title.includes(expectedTitle), `页面标题应包含: ${expectedTitle}`);
  }

  /**
   * 截图保存
   * @param filename - 文件名
   */
  async takeScreenshot(filename: string): Promise<void> {
    await this.page.screenshot({ path: `test-results/${filename}` });
  }
}
