import { Page } from '@playwright/test';
import { BasePage } from './base-page';
import { Assert } from '../utils/assertions';
import { TestData } from '../data/test-data';
import { OptimizedAiQueries, FirstNewsInfo } from '../utils/optimized-ai-queries';

/**
 * 新闻页面对象
 */
export class NewsPage extends BasePage {
  constructor(page: Page, assert: Assert, ai: any, aiQuery: any) {
    super(page, assert, ai, aiQuery);
  }

  /**
   * 获取第一条新闻的发布时间
   * @returns 发布时间
   */
  async getFirstNewsTime(): Promise<string> {
    const timeResult = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsTime);
    console.log(`AI查询时间原始结果: "${timeResult}"`);

    // 尝试从结果中提取时间信息
    let time = timeResult;
    if (typeof timeResult === 'string') {
      // 尝试提取常见的时间格式
      const timePatterns = [
        /(\d{4}-\d{2}-\d{2})/,           // 2025-07-22
        /(\d{2}:\d{2})/,                 // 14:30
        /(\d+分钟前)/,                   // 30分钟前
        /(\d+小时前)/,                   // 2小时前
        /(\d+天前)/,                     // 3天前
        /(近日|今日|昨日)/,              // 近日、今日、昨日
        /(\d{4}年\d{1,2}月\d{1,2}日)/,   // 2025年7月22日
        /(\d{1,2}月\d{1,2}日)/,          // 7月22日
        /(刚刚)/                         // 刚刚
      ];

      for (const pattern of timePatterns) {
        const match = timeResult.match(pattern);
        if (match) {
          time = match[1];
          break;
        }
      }
    }

    console.log(`提取的时间信息: "${time}"`);
    this.assert.isTruthy(time, `${TestData.messages.errors.timeEmpty}，AI返回: "${timeResult}"`);
    return time;
  }

  /**
   * 获取第一条新闻的来源
   * @returns 新闻来源
   */
  async getFirstNewsSource(): Promise<string> {
    const sourceResult = await this.performAiQuery(TestData.aiCommands.query.getFirstNewsSource);
    console.log(`AI查询来源原始结果: "${sourceResult}"`);

    // 尝试从结果中提取来源信息
    let source = sourceResult;
    if (typeof sourceResult === 'string') {
      // 如果结果太长，可能包含了其他信息，尝试提取来源
      if (sourceResult.length > 50) {
        // 尝试提取常见的来源格式
        const sourcePatterns = [
          /来源[：:]\s*([^\s\n]+)/,        // 来源：xxx
          /([^\s\n]+)\s*报道/,            // xxx报道
          /([^\s\n]+)\s*消息/,            // xxx消息
          /记者[：:]\s*([^\s\n]+)/        // 记者：xxx
        ];

        for (const pattern of sourcePatterns) {
          const match = sourceResult.match(pattern);
          if (match) {
            source = match[1];
            break;
          }
        }
      }
    }

    console.log(`提取的来源信息: "${source}"`);
    this.assert.isTruthy(source, `${TestData.messages.errors.sourceEmpty}，AI返回: "${sourceResult}"`);
    return source;
  }

  /**
   * 获取最新新闻数据（优化版）
   * @returns 包含时间和来源的对象
   */
  async getLatestNewsData(): Promise<{ time: string; source: string }> {
    // 使用优化的复合查询获取第一条新闻信息
    const newsInfo = await OptimizedAiQueries.getFirstNewsInfo(this);

    return { time: newsInfo.time, source: newsInfo.source };
  }

  /**
   * 验证新闻列表是否加载
   */
  async verifyNewsListLoaded(): Promise<void> {
    // 可以添加具体的验证逻辑
    // 例如检查是否有新闻项目存在
    await this.waitForPageLoad();
  }
}
