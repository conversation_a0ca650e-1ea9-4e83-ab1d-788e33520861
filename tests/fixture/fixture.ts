import { test as base } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { Assert } from '../utils/assertions';
import { MidsceneUtils, AiCallCache } from '../utils/unified-midscene-utils';
import { AiCallMonitor } from '../utils/ai-call-monitor';

// 在导入 PlaywrightAiFixture 前应用 Midscene 补丁
// 补丁已经在 unified-midscene-utils.ts 中自动应用

// 导入 PlaywrightAiFixture
import { PlaywrightAiFixture } from '@midscene/web/playwright';

// Define a new type that includes our custom assertion fixture
type MyFixtures = PlayWrightAiFixtureType & {
  assert: Assert;
};

// Extend the base test with both AI fixtures and our custom assertion fixture
export const test = base.extend<MyFixtures>({
  // Inherit the AI fixtures (已经通过补丁修改)
  ...PlaywrightAiFixture(),

  // Add our custom assert fixture
  assert: async ({ page, aiAssert }, use) => {
    await use(new Assert(page, aiAssert));
  },
});

// Windows 兼容性和缓存清理已通过 unified-midscene-utils.ts 和 npm scripts 自动处理
test.beforeAll(async () => {
  // 确保 Windows 兼容性设置已应用
  MidsceneUtils.setupWindowsCompatibility();

  // 清理过期的AI调用缓存
  AiCallCache.clearExpired();
});

// 在每个测试完成后合并统计数据
test.afterEach(async () => {
  AiCallMonitor.mergeToGlobalStats();
});

// 在测试文件完成后合并统计并尝试打印全局报告
test.afterAll(async () => {
  // 合并当前统计到全局统计
  AiCallMonitor.mergeToGlobalStats();

  // 清理所有缓存
  AiCallCache.clearAll();
  
  // 使用Promise来确保异步操作完成
  await new Promise<void>((resolve) => {
    setTimeout(() => {
      // 使用文件锁机制确保只打印一次
      try {
        const fs = eval('require')('fs');
        const path = eval('require')('path');
        const lockFile = path.join(process.cwd(), '.ai-report-lock');
        
        // 尝试创建锁文件，如果已存在则说明已经有其他进程在打印报告
        fs.writeFileSync(lockFile, process.pid.toString(), { flag: 'wx' });
        
        // 延迟一下，让其他进程有机会完成统计合并
        setTimeout(() => {
          const stats = AiCallMonitor.loadAndMergeAllStats();
          if (stats && stats.totalCalls > 0) {
            console.log('\n📊 AI调用统计报告');
            console.log('='.repeat(50));
            console.log(`总调用次数: ${stats.totalCalls}`);
            console.log(`成功调用: ${stats.successfulCalls} (${((stats.successfulCalls / stats.totalCalls) * 100).toFixed(1)}%)`);
            console.log(`失败调用: ${stats.failedCalls} (${((stats.failedCalls / stats.totalCalls) * 100).toFixed(1)}%)`);
            console.log(`缓存命中: ${stats.cacheHits} (${((stats.cacheHits / stats.totalCalls) * 100).toFixed(1)}%)`);
            console.log(`平均耗时: ${stats.averageDuration.toFixed(0)}ms`);
            console.log(`总耗时: ${stats.totalDuration}ms`);
            
            console.log('\n按类型统计:');
            for (const [type, count] of Object.entries(stats.callsByType)) {
              console.log(`  ${type}: ${count}`);
            }

            if (Object.keys(stats.errorsByType).length > 0) {
              console.log('\n错误统计:');
              for (const [error, count] of Object.entries(stats.errorsByType)) {
                console.log(`  ${error}: ${count}`);
              }
            }
            console.log('='.repeat(50));
            
            // 清理统计文件
            AiCallMonitor.cleanupStatsFiles();
          }
          
          // 清理锁文件
          try {
            fs.unlinkSync(lockFile);
          } catch (e) {
            // 忽略清理错误
          }
          
          resolve();
        }, 1000);
        
      } catch (error) {
        // 锁文件已存在，说明其他进程正在处理，直接返回
        resolve();
      }
    }, 1500);
  });
});
