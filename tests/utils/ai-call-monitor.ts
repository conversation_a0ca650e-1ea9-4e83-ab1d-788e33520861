/**
 * AI调用监控和统计工具
 * 用于监控AI调用频次、成功率和性能
 */

export interface AiCallStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  cacheHits: number;
  totalDuration: number;
  averageDuration: number;
  callsByType: Record<string, number>;
  errorsByType: Record<string, number>;
}

export interface AiCallRecord {
  id: string;
  type: 'action' | 'query' | 'assert';
  command: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  cached: boolean;
  error?: string;
  pageUrl?: string;
}

/**
 * AI调用监控器
 */
export class AiCallMonitor {
  private static calls: AiCallRecord[] = [];
  private static isEnabled = process.env.AI_MONITOR_ENABLED !== 'false';
  private static globalStats: AiCallStats | null = null;
  private static reportPrinted = false;

  /**
   * 开始记录AI调用
   */
  static startCall(type: 'action' | 'query' | 'assert', command: string, pageUrl?: string): string {
    if (!this.isEnabled) return '';

    const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const record: AiCallRecord = {
      id,
      type,
      command,
      startTime: Date.now(),
      success: false,
      cached: false,
      pageUrl
    };

    this.calls.push(record);
    return id;
  }

  /**
   * 结束记录AI调用
   */
  static endCall(id: string, success: boolean, cached: boolean = false, error?: string): void {
    if (!this.isEnabled || !id) return;

    const record = this.calls.find(call => call.id === id);
    if (record) {
      record.endTime = Date.now();
      record.duration = record.endTime - record.startTime;
      record.success = success;
      record.cached = cached;
      record.error = error;
    }
  }

  /**
   * 记录缓存命中
   */
  static recordCacheHit(type: 'action' | 'query' | 'assert', command: string): void {
    if (!this.isEnabled) return;

    const id = this.startCall(type, command);
    this.endCall(id, true, true);
  }

  /**
   * 获取统计信息
   */
  static getStats(): AiCallStats {
    const stats: AiCallStats = {
      totalCalls: this.calls.length,
      successfulCalls: 0,
      failedCalls: 0,
      cacheHits: 0,
      totalDuration: 0,
      averageDuration: 0,
      callsByType: {},
      errorsByType: {}
    };

    for (const call of this.calls) {
      // 统计成功/失败
      if (call.success) {
        stats.successfulCalls++;
      } else {
        stats.failedCalls++;
      }

      // 统计缓存命中
      if (call.cached) {
        stats.cacheHits++;
      }

      // 统计持续时间
      if (call.duration) {
        stats.totalDuration += call.duration;
      }

      // 按类型统计
      stats.callsByType[call.type] = (stats.callsByType[call.type] || 0) + 1;

      // 错误统计
      if (call.error) {
        stats.errorsByType[call.error] = (stats.errorsByType[call.error] || 0) + 1;
      }
    }

    // 计算平均持续时间
    if (stats.totalCalls > 0) {
      stats.averageDuration = stats.totalDuration / stats.totalCalls;
    }

    return stats;
  }

  /**
   * 打印统计报告
   */
  static printReport(): void {
    if (!this.isEnabled) return;

    const stats = this.getStats();
    console.log('\n📊 AI调用统计报告');
    console.log('='.repeat(50));
    console.log(`总调用次数: ${stats.totalCalls}`);
    console.log(`成功调用: ${stats.successfulCalls} (${((stats.successfulCalls / stats.totalCalls) * 100).toFixed(1)}%)`);
    console.log(`失败调用: ${stats.failedCalls} (${((stats.failedCalls / stats.totalCalls) * 100).toFixed(1)}%)`);
    console.log(`缓存命中: ${stats.cacheHits} (${((stats.cacheHits / stats.totalCalls) * 100).toFixed(1)}%)`);
    console.log(`平均耗时: ${stats.averageDuration.toFixed(0)}ms`);
    console.log(`总耗时: ${stats.totalDuration}ms`);
    
    console.log('\n按类型统计:');
    for (const [type, count] of Object.entries(stats.callsByType)) {
      console.log(`  ${type}: ${count}`);
    }

    if (Object.keys(stats.errorsByType).length > 0) {
      console.log('\n错误统计:');
      for (const [error, count] of Object.entries(stats.errorsByType)) {
        console.log(`  ${error}: ${count}`);
      }
    }
    console.log('='.repeat(50));
  }

  /**
   * 清除统计数据
   */
  static clear(): void {
    this.calls = [];
  }

  /**
   * 启用/禁用监控
   */
  static setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 获取最近的调用记录
   */
  static getRecentCalls(limit: number = 10): AiCallRecord[] {
    return this.calls.slice(-limit);
  }

  /**
   * 合并统计数据到全局统计
   */
  static mergeToGlobalStats(): void {
    if (!this.isEnabled) return;

    const currentStats = this.getStats();
    
    if (!this.globalStats) {
      this.globalStats = { ...currentStats };
    } else {
      // 合并统计数据
      this.globalStats.totalCalls += currentStats.totalCalls;
      this.globalStats.successfulCalls += currentStats.successfulCalls;
      this.globalStats.failedCalls += currentStats.failedCalls;
      this.globalStats.cacheHits += currentStats.cacheHits;
      this.globalStats.totalDuration += currentStats.totalDuration;

      // 合并按类型统计
      for (const [type, count] of Object.entries(currentStats.callsByType)) {
        this.globalStats.callsByType[type] = (this.globalStats.callsByType[type] || 0) + count;
      }

      // 合并错误统计
      for (const [error, count] of Object.entries(currentStats.errorsByType)) {
        this.globalStats.errorsByType[error] = (this.globalStats.errorsByType[error] || 0) + count;
      }

      // 重新计算平均持续时间
      if (this.globalStats.totalCalls > 0) {
        this.globalStats.averageDuration = this.globalStats.totalDuration / this.globalStats.totalCalls;
      }
    }

    // 将统计数据写入共享文件
    this.saveStatsToFile();
  }

  /**
   * 将统计数据保存到文件
   */
  static saveStatsToFile(): void {
    if (!this.isEnabled || !this.globalStats) return;

    try {
      const fs = eval('require')('fs');
      const path = eval('require')('path');
      const statsDir = path.join(process.cwd(), '.ai-stats');
      const statsFile = path.join(statsDir, `stats-${process.pid}.json`);

      // 确保目录存在
      if (!fs.existsSync(statsDir)) {
        fs.mkdirSync(statsDir, { recursive: true });
      }

      // 保存统计数据
      fs.writeFileSync(statsFile, JSON.stringify(this.globalStats, null, 2));
    } catch (error) {
      // 忽略文件写入错误
    }
  }

  /**
   * 从所有文件中读取并合并统计数据
   */
  static loadAndMergeAllStats(): AiCallStats | null {
    try {
      const fs = eval('require')('fs');
      const path = eval('require')('path');
      const statsDir = path.join(process.cwd(), '.ai-stats');

      if (!fs.existsSync(statsDir)) {
        return null;
      }

      const files = fs.readdirSync(statsDir).filter((file: string) => file.startsWith('stats-') && file.endsWith('.json'));
      
      if (files.length === 0) {
        return null;
      }

      let mergedStats: AiCallStats | null = null;

      for (const file of files) {
        try {
          const filePath = path.join(statsDir, file);
          const content = fs.readFileSync(filePath, 'utf8');
          const stats: AiCallStats = JSON.parse(content);

          if (!mergedStats) {
            mergedStats = { ...stats };
          } else {
            // 合并统计数据
            mergedStats.totalCalls += stats.totalCalls;
            mergedStats.successfulCalls += stats.successfulCalls;
            mergedStats.failedCalls += stats.failedCalls;
            mergedStats.cacheHits += stats.cacheHits;
            mergedStats.totalDuration += stats.totalDuration;

            // 合并按类型统计
            for (const [type, count] of Object.entries(stats.callsByType)) {
              mergedStats.callsByType[type] = (mergedStats.callsByType[type] || 0) + count;
            }

            // 合并错误统计
            for (const [error, count] of Object.entries(stats.errorsByType)) {
              mergedStats.errorsByType[error] = (mergedStats.errorsByType[error] || 0) + count;
            }
          }
        } catch (e) {
          // 忽略单个文件读取错误
        }
      }

      // 重新计算平均持续时间
      if (mergedStats && mergedStats.totalCalls > 0) {
        mergedStats.averageDuration = mergedStats.totalDuration / mergedStats.totalCalls;
      }

      return mergedStats;
    } catch (error) {
      return null;
    }
  }

  /**
   * 清理统计文件
   */
  static cleanupStatsFiles(): void {
    try {
      const fs = eval('require')('fs');
      const path = eval('require')('path');
      const statsDir = path.join(process.cwd(), '.ai-stats');

      if (fs.existsSync(statsDir)) {
        const files = fs.readdirSync(statsDir);
        for (const file of files) {
          fs.unlinkSync(path.join(statsDir, file));
        }
        fs.rmdirSync(statsDir);
      }
    } catch (error) {
      // 忽略清理错误
    }
  }

  /**
   * 打印全局统计报告（只打印一次）
   */
  static printGlobalReport(): void {
    if (!this.isEnabled || this.reportPrinted) return;

    // 使用文件锁机制确保只打印一次
    try {
      const fs = eval('require')('fs');
      const path = eval('require')('path');
      const lockFile = path.join(process.cwd(), '.ai-report-lock');
      
      // 尝试创建锁文件，如果已存在则说明已经有其他进程在打印报告
      fs.writeFileSync(lockFile, process.pid.toString(), { flag: 'wx' });
      
      // 延迟一下，让其他进程有机会完成统计合并
      setTimeout(() => {
        // 从文件中读取并合并所有统计数据
        const mergedStats = this.loadAndMergeAllStats();
        
        if (!mergedStats || mergedStats.totalCalls === 0) {
          // 清理锁文件
          try {
            fs.unlinkSync(lockFile);
          } catch (e) {
            // 忽略清理错误
          }
          return;
        }
        
        console.log('\n📊 AI调用统计报告');
        console.log('='.repeat(50));
        console.log(`总调用次数: ${mergedStats.totalCalls}`);
        console.log(`成功调用: ${mergedStats.successfulCalls} (${((mergedStats.successfulCalls / mergedStats.totalCalls) * 100).toFixed(1)}%)`);
        console.log(`失败调用: ${mergedStats.failedCalls} (${((mergedStats.failedCalls / mergedStats.totalCalls) * 100).toFixed(1)}%)`);
        console.log(`缓存命中: ${mergedStats.cacheHits} (${((mergedStats.cacheHits / mergedStats.totalCalls) * 100).toFixed(1)}%)`);
        console.log(`平均耗时: ${mergedStats.averageDuration.toFixed(0)}ms`);
        console.log(`总耗时: ${mergedStats.totalDuration}ms`);
        
        console.log('\n按类型统计:');
        for (const [type, count] of Object.entries(mergedStats.callsByType)) {
          console.log(`  ${type}: ${count}`);
        }

        if (Object.keys(mergedStats.errorsByType).length > 0) {
          console.log('\n错误统计:');
          for (const [error, count] of Object.entries(mergedStats.errorsByType)) {
            console.log(`  ${error}: ${count}`);
          }
        }
        console.log('='.repeat(50));

        this.reportPrinted = true;
        
        // 清理统计文件和锁文件
        this.cleanupStatsFiles();
        try {
          fs.unlinkSync(lockFile);
        } catch (e) {
          // 忽略清理错误
        }
      }, 500);
      
    } catch (error) {
      // 锁文件已存在，说明其他进程正在处理，直接返回
      return;
    }
  }

  /**
   * 重置全局统计状态
   */
  static resetGlobalStats(): void {
    this.globalStats = null;
    this.reportPrinted = false;
  }
}

// 移除自动打印，改为在测试结束时合并统计数据
if (typeof process !== 'undefined') {
  process.on('exit', () => {
    // 只合并数据，不打印报告
    AiCallMonitor.mergeToGlobalStats();
  });
}
