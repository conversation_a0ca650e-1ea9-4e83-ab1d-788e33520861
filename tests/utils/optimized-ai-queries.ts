/**
 * 优化的AI查询工具
 * 提供复合查询和智能降级策略
 */

import { BasePage } from '../pages/base-page';
import { TestData } from '../data/test-data';

export interface HomeNewsListInfo {
  visible: boolean;
  count: number;
  titles: string[];
}

export interface FirstNewsInfo {
  time: string;
  source: string;
}

/**
 * 优化的AI查询工具类
 */
export class OptimizedAiQueries {
  
  /**
   * 获取首页新闻列表完整信息（复合查询）
   * 替代多个单独的AI调用
   */
  static async getHomeNewsListInfo(basePage: BasePage): Promise<HomeNewsListInfo> {
    try {
      // 尝试使用复合查询
      const result = await basePage.performAiQuery(TestData.aiCommands.query.getHomeNewsListInfo);
      
      // 尝试解析JSON结果
      let parsedResult: any;
      try {
        parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
      } catch {
        // JSON解析失败，降级到单独查询
        return await this.getHomeNewsListInfoFallback(basePage);
      }

      // 验证结果结构
      if (this.isValidHomeNewsListInfo(parsedResult)) {
        return parsedResult;
      } else {
        return await this.getHomeNewsListInfoFallback(basePage);
      }

    } catch (error) {
      return await this.getHomeNewsListInfoFallback(basePage);
    }
  }

  /**
   * 获取第一条新闻完整信息（复合查询）
   */
  static async getFirstNewsInfo(basePage: BasePage): Promise<FirstNewsInfo> {
    try {
      // 尝试使用复合查询
      const result = await basePage.performAiQuery(TestData.aiCommands.query.getFirstNewsInfo);
      
      // 尝试解析JSON结果
      let parsedResult: any;
      try {
        parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
      } catch {
        // JSON解析失败，降级到单独查询
        return await this.getFirstNewsInfoFallback(basePage);
      }

      // 验证结果结构
      if (this.isValidFirstNewsInfo(parsedResult)) {
        return parsedResult;
      } else {
        return await this.getFirstNewsInfoFallback(basePage);
      }

    } catch (error) {
      return await this.getFirstNewsInfoFallback(basePage);
    }
  }

  /**
   * 降级策略：单独查询首页新闻列表信息
   */
  private static async getHomeNewsListInfoFallback(basePage: BasePage): Promise<HomeNewsListInfo> {
    const result: HomeNewsListInfo = {
      visible: false,
      count: 0,
      titles: []
    };

    try {
      // 检查可见性
      const visibleResult = await basePage.performAiQuery(TestData.aiCommands.query.checkNewsListVisible);
      result.visible = this.parseBoolean(visibleResult);
    } catch (error) {
      result.visible = await this.basicVisibilityCheck(basePage);
    }

    if (result.visible) {
      try {
        // 获取数量
        const countResult = await basePage.performAiQuery(TestData.aiCommands.query.getHomeNewsListCount);
        result.count = this.parseNumber(countResult);
      } catch (error) {
        result.count = 0;
      }

      try {
        // 获取标题
        const titlesResult = await basePage.performAiQuery(TestData.aiCommands.query.getHomeNewsListTitles);
        result.titles = this.parseArray(titlesResult);
      } catch (error) {
        result.titles = [];
      }
    }

    return result;
  }

  /**
   * 降级策略：单独查询第一条新闻信息
   */
  private static async getFirstNewsInfoFallback(basePage: BasePage): Promise<FirstNewsInfo> {
    const result: FirstNewsInfo = {
      time: '',
      source: ''
    };

    try {
      const timeResult = await basePage.performAiQuery(TestData.aiCommands.query.getFirstNewsTime);
      result.time = this.parseString(timeResult);
    } catch (error) {
      // 静默处理错误
    }

    try {
      const sourceResult = await basePage.performAiQuery(TestData.aiCommands.query.getFirstNewsSource);
      result.source = this.parseString(sourceResult);
    } catch (error) {
      // 静默处理错误
    }

    return result;
  }

  /**
   * 基础可见性检查（不使用AI）
   */
  private static async basicVisibilityCheck(basePage: BasePage): Promise<boolean> {
    try {
      const pageContent = await basePage['page'].textContent('body');
      return !!(pageContent && (
        pageContent.includes('新闻') ||
        pageContent.includes('资讯') ||
        pageContent.includes('最新') ||
        pageContent.includes('热点')
      ));
    } catch {
      return false;
    }
  }

  // 辅助方法：验证和解析结果
  private static isValidHomeNewsListInfo(obj: any): obj is HomeNewsListInfo {
    return obj && 
           typeof obj.visible === 'boolean' &&
           typeof obj.count === 'number' &&
           Array.isArray(obj.titles);
  }

  private static isValidFirstNewsInfo(obj: any): obj is FirstNewsInfo {
    return obj && 
           typeof obj.time === 'string' &&
           typeof obj.source === 'string';
  }

  private static parseBoolean(value: any): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      return value.toLowerCase().includes('true') || value.toLowerCase().includes('是');
    }
    return false;
  }

  private static parseNumber(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const match = value.match(/\d+/);
      return match ? parseInt(match[0]) : 0;
    }
    return 0;
  }

  private static parseString(value: any): string {
    return String(value || '').trim();
  }

  private static parseArray(value: any): string[] {
    if (Array.isArray(value)) return value;
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return value.split('\n').filter(item => item.trim().length > 0);
      }
    }
    return [];
  }
}
