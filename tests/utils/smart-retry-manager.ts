/**
 * 智能重试和降级管理器
 * 为AI调用提供智能重试策略和降级机制
 */

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  enableFallback: boolean;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  usedFallback: boolean;
  totalDuration: number;
}

/**
 * 智能重试管理器
 */
export class SmartRetryManager {
  private static defaultConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    enableFallback: true
  };

  /**
   * 执行带智能重试的操作
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    fallbackOperation?: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<RetryResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    let lastError: Error = new Error('未知错误');
    let attempts = 0;

    // 主要操作重试
    for (let i = 0; i < finalConfig.maxRetries; i++) {
      attempts++;
      try {
        const result = await operation();

        return {
          success: true,
          result,
          attempts,
          usedFallback: false,
          totalDuration: Date.now() - startTime
        };
      } catch (error) {
        lastError = error;

        // 如果不是最后一次尝试，等待后重试
        if (i < finalConfig.maxRetries - 1) {
          const delay = Math.min(
            finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, i),
            finalConfig.maxDelay
          );
          await this.sleep(delay);
        }
      }
    }

    // 如果启用降级且有降级操作
    if (finalConfig.enableFallback && fallbackOperation) {
      try {
        const result = await fallbackOperation();
        return {
          success: true,
          result,
          attempts,
          usedFallback: true,
          totalDuration: Date.now() - startTime
        };
      } catch (fallbackError) {
        lastError = fallbackError;
      }
    }

    // 所有尝试都失败了
    return {
      success: false,
      error: lastError,
      attempts,
      usedFallback: false,
      totalDuration: Date.now() - startTime
    };
  }

  /**
   * 执行带条件重试的操作
   * 根据错误类型决定是否重试
   */
  static async executeWithConditionalRetry<T>(
    operation: () => Promise<T>,
    shouldRetry: (error: Error) => boolean,
    fallbackOperation?: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<RetryResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    let lastError: Error = new Error('未知错误');
    let attempts = 0;

    for (let i = 0; i < finalConfig.maxRetries; i++) {
      attempts++;
      try {
        const result = await operation();
        return {
          success: true,
          result,
          attempts,
          usedFallback: false,
          totalDuration: Date.now() - startTime
        };
      } catch (error) {
        lastError = error;

        // 检查是否应该重试
        if (!shouldRetry(error)) {
          break;
        }

        // 如果不是最后一次尝试，等待后重试
        if (i < finalConfig.maxRetries - 1) {
          const delay = Math.min(
            finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, i),
            finalConfig.maxDelay
          );
          await this.sleep(delay);
        }
      }
    }

    // 尝试降级操作
    if (finalConfig.enableFallback && fallbackOperation) {
      try {
        const result = await fallbackOperation();
        return {
          success: true,
          result,
          attempts,
          usedFallback: true,
          totalDuration: Date.now() - startTime
        };
      } catch (fallbackError) {
        lastError = fallbackError;
      }
    }

    return {
      success: false,
      error: lastError,
      attempts,
      usedFallback: false,
      totalDuration: Date.now() - startTime
    };
  }

  /**
   * 批量执行操作，支持部分失败
   */
  static async executeBatch<T>(
    operations: Array<() => Promise<T>>,
    config: Partial<RetryConfig> = {}
  ): Promise<Array<RetryResult<T>>> {
    const results: Array<RetryResult<T>> = [];

    for (let i = 0; i < operations.length; i++) {
      const result = await this.executeWithRetry(operations[i], undefined, config);
      results.push(result);
    }

    return results;
  }

  /**
   * 检查错误是否适合重试
   */
  static isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /timeout/i,
      /network/i,
      /connection/i,
      /temporary/i,
      /rate limit/i,
      /server error/i,
      /503/,
      /502/,
      /504/
    ];

    const nonRetryablePatterns = [
      /authentication/i,
      /authorization/i,
      /forbidden/i,
      /not found/i,
      /400/,
      /401/,
      /403/,
      /404/
    ];

    const errorMessage = error.message.toLowerCase();

    // 检查不可重试的错误
    for (const pattern of nonRetryablePatterns) {
      if (pattern.test(errorMessage)) {
        return false;
      }
    }

    // 检查可重试的错误
    for (const pattern of retryablePatterns) {
      if (pattern.test(errorMessage)) {
        return true;
      }
    }

    // 默认情况下，未知错误可以重试
    return true;
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建预配置的重试器
   */
  static createRetrier(config: Partial<RetryConfig>) {
    return {
      execute: <T>(
        operation: () => Promise<T>,
        fallbackOperation?: () => Promise<T>
      ) => this.executeWithRetry(operation, fallbackOperation, config)
    };
  }
}

// 预定义的重试配置
export const RetryConfigs = {
  // 快速重试 - 适用于轻量级操作
  fast: {
    maxRetries: 2,
    baseDelay: 500,
    maxDelay: 2000,
    backoffMultiplier: 1.5,
    enableFallback: true
  },
  
  // 标准重试 - 适用于一般AI操作
  standard: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffMultiplier: 2,
    enableFallback: true
  },
  
  // 耐心重试 - 适用于复杂AI操作
  patient: {
    maxRetries: 5,
    baseDelay: 2000,
    maxDelay: 15000,
    backoffMultiplier: 2,
    enableFallback: true
  },
  
  // 无重试 - 快速失败
  noRetry: {
    maxRetries: 1,
    baseDelay: 0,
    maxDelay: 0,
    backoffMultiplier: 1,
    enableFallback: true
  }
};
