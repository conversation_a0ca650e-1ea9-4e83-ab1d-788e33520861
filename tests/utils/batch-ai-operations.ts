/**
 * 批量AI操作优化工具
 * 用于优化多个AI操作的执行，减少总体调用次数
 */

import { BasePage } from '../pages/base-page';
import { SmartRetryManager, RetryConfigs } from './smart-retry-manager';

export interface BatchOperation {
  id: string;
  type: 'action' | 'query';
  command: string;
  priority: number; // 1-10, 10为最高优先级
}

export interface BatchResult {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
}

/**
 * 批量AI操作管理器
 */
export class BatchAiOperations {
  
  /**
   * 执行批量AI操作（按优先级排序）
   */
  static async executeBatch(
    basePage: BasePage,
    operations: BatchOperation[]
  ): Promise<BatchResult[]> {
    // 按优先级排序（高优先级先执行）
    const sortedOperations = operations.sort((a, b) => b.priority - a.priority);

    const results: BatchResult[] = [];

    for (let i = 0; i < sortedOperations.length; i++) {
      const operation = sortedOperations[i];
      const startTime = Date.now();

      try {
        let result: any;

        if (operation.type === 'action') {
          await basePage.performAiAction(operation.command);
          result = 'success';
        } else {
          result = await basePage.performAiQuery(operation.command);
        }

        results.push({
          id: operation.id,
          success: true,
          result,
          duration: Date.now() - startTime
        });

      } catch (error) {
        results.push({
          id: operation.id,
          success: false,
          error: error.message,
          duration: Date.now() - startTime
        });

        // 根据优先级决定是否继续
        if (operation.priority >= 8) {
          break;
        }
      }

      // 在操作之间添加小延迟，避免过于频繁的AI调用
      if (i < sortedOperations.length - 1) {
        await this.sleep(200);
      }
    }

    return results;
  }

  /**
   * 执行并行AI查询（仅限查询操作）
   */
  static async executeParallelQueries(
    basePage: BasePage,
    queries: Array<{ id: string; command: string }>
  ): Promise<BatchResult[]> {
    const promises = queries.map(async (query) => {
      const startTime = Date.now();
      try {
        const result = await basePage.performAiQuery(query.command);
        return {
          id: query.id,
          success: true,
          result,
          duration: Date.now() - startTime
        };
      } catch (error) {
        return {
          id: query.id,
          success: false,
          error: error.message,
          duration: Date.now() - startTime
        };
      }
    });

    const results = await Promise.allSettled(promises);

    const batchResults: BatchResult[] = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          id: queries[index].id,
          success: false,
          error: result.reason?.message || '未知错误',
          duration: 0
        };
      }
    });

    return batchResults;
  }

  /**
   * 智能批量执行 - 自动选择串行或并行
   */
  static async executeIntelligentBatch(
    basePage: BasePage,
    operations: BatchOperation[]
  ): Promise<BatchResult[]> {
    // 分离操作和查询
    const actions = operations.filter(op => op.type === 'action');
    const queries = operations.filter(op => op.type === 'query');

    const results: BatchResult[] = [];

    // 先执行所有操作（必须串行）
    if (actions.length > 0) {
      const actionResults = await this.executeBatch(basePage, actions);
      results.push(...actionResults);

      // 检查是否有关键操作失败
      const criticalFailures = actionResults.filter(r => !r.success &&
        actions.find(a => a.id === r.id)?.priority >= 8);

      if (criticalFailures.length > 0) {
        return results;
      }
    }

    // 再执行所有查询（可以并行）
    if (queries.length > 0) {
      if (queries.length <= 3) {
        // 少量查询使用并行
        const queryResults = await this.executeParallelQueries(
          basePage,
          queries.map(q => ({ id: q.id, command: q.command }))
        );
        results.push(...queryResults);
      } else {
        // 大量查询使用串行（避免过载）
        const queryResults = await this.executeBatch(basePage, queries);
        results.push(...queryResults);
      }
    }

    return results;
  }

  /**
   * 创建批量操作构建器
   */
  static createBuilder() {
    const operations: BatchOperation[] = [];
    
    return {
      addAction(id: string, command: string, priority: number = 5) {
        operations.push({ id, type: 'action', command, priority });
        return this;
      },
      
      addQuery(id: string, command: string, priority: number = 5) {
        operations.push({ id, type: 'query', command, priority });
        return this;
      },
      
      addHighPriorityAction(id: string, command: string) {
        return this.addAction(id, command, 9);
      },
      
      addLowPriorityQuery(id: string, command: string) {
        return this.addQuery(id, command, 2);
      },
      
      build(): BatchOperation[] {
        return [...operations];
      },
      
      async execute(basePage: BasePage): Promise<BatchResult[]> {
        return BatchAiOperations.executeIntelligentBatch(basePage, operations);
      }
    };
  }

  /**
   * 分析批量结果
   */
  static analyzeBatchResults(results: BatchResult[]): {
    successRate: number;
    totalDuration: number;
    averageDuration: number;
    failedOperations: string[];
    slowOperations: string[];
  } {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    const averageDuration = totalDuration / results.length;
    
    // 识别慢操作（超过平均时间2倍）
    const slowThreshold = averageDuration * 2;
    const slowOperations = results
      .filter(r => r.duration > slowThreshold)
      .map(r => r.id);
    
    return {
      successRate: (successful.length / results.length) * 100,
      totalDuration,
      averageDuration,
      failedOperations: failed.map(r => r.id),
      slowOperations
    };
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 预定义的常用批量操作
export const CommonBatchOperations = {
  /**
   * 登录操作序列
   */
  loginSequence: () => BatchAiOperations.createBuilder()
    .addHighPriorityAction('click-login', '点击"登录"')
    .addHighPriorityAction('click-account-login', '点击"账号密码登录"')
    .addHighPriorityAction('enter-phone', '在"手机号"输入框中输入"***********"')
    .addHighPriorityAction('enter-password', '在"密码"输入框中输入"8131197.asd"')
    .addHighPriorityAction('click-login-button', '点击"登录"按钮')
    .build(),

  /**
   * 新闻页面验证序列
   */
  newsValidationSequence: () => BatchAiOperations.createBuilder()
    .addQuery('check-news-visible', 'boolean, 检查首页是否存在新闻列表区域且内容正常显示', 8)
    .addQuery('get-news-count', 'number, 统计首页新闻列表中可见的新闻条目数量，只返回数字', 6)
    .addQuery('get-news-titles', 'array, 获取首页新闻列表中前10条新闻的标题，返回字符串数组格式', 4)
    .build()
};
